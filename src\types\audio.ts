/**
 * COMPREHENSIVE AUDIO TYPE DEFINITIONS
 * 
 * TypeScript interfaces and types for VoiceHealth AI audio services
 * with strict type safety, encryption support, and emergency protocols.
 */

// =============================================================================
// CORE AUDIO TYPES
// =============================================================================

export interface AudioBlob extends Blob {
  readonly size: number;
  readonly type: string;
}

export interface AudioBuffer {
  readonly sampleRate: number;
  readonly numberOfChannels: number;
  readonly duration: number;
  getChannelData(channel: number): Float32Array;
}

export interface AudioMetadata {
  readonly sampleRate: number;
  readonly duration: number;
  readonly channels: number;
  readonly rms: string;
  readonly peak: string;
  readonly dynamicRange: string;
  readonly bitRate?: number;
  readonly format: string;
  readonly codec: string;
}

// =============================================================================
// VALIDATION TYPES
// =============================================================================

export interface ValidationResult {
  readonly valid: boolean;
  readonly errors: readonly string[];
  readonly warnings: readonly string[];
  readonly metadata: AudioMetadata;
  readonly securityChecks: SecurityValidationResult;
}

export interface SecurityValidationResult {
  readonly safe: boolean;
  readonly threats: readonly string[];
  readonly checks: {
    readonly headerValidation: boolean;
    readonly metadataValidation: boolean;
    readonly contentValidation: boolean;
    readonly sizeConsistency: boolean;
  };
}

export interface BasicValidationResult {
  readonly valid: boolean;
  readonly errors: readonly string[];
}

export interface FormatValidationResult {
  readonly valid: boolean;
  readonly errors: readonly string[];
  readonly warnings: readonly string[];
}

export interface QualityValidationResult {
  readonly acceptable: boolean;
  readonly warnings: readonly string[];
  readonly metadata: AudioMetadata;
}

export interface RealtimeValidationResult {
  readonly valid: boolean;
  readonly errors: readonly string[];
}

export interface ValidationOptions {
  readonly realTime?: boolean;
  readonly strictMode?: boolean;
  readonly emergencyOverride?: boolean;
}

// =============================================================================
// CHECKSUM AND INTEGRITY TYPES
// =============================================================================

export interface ChecksumResult {
  readonly checksum: string;
  readonly algorithm: 'SHA-256';
  readonly timestamp: string;
}

export interface IntegrityVerificationResult {
  readonly valid: boolean;
  readonly originalChecksumMatch: boolean;
  readonly encryptedChecksumMatch: boolean;
  readonly errors: readonly string[];
  readonly warnings: readonly string[];
}

export interface IntegrityMetadata {
  readonly originalChecksum: string;
  readonly encryptedChecksum: string;
  readonly algorithm: 'SHA-256';
  readonly verified: boolean;
  readonly lastChecked?: string;
}

// =============================================================================
// ENCRYPTION TYPES
// =============================================================================

export interface EncryptedAudioData {
  readonly encrypted: boolean;
  readonly algorithm: 'AES-256-GCM';
  readonly keyLength: number;
  readonly iv: string;
  readonly authTag: string;
  readonly encryptedData: string;
  readonly timestamp: string;
}

export interface DecryptedAudioData {
  readonly audioData: readonly number[];
  readonly originalSize: number;
  readonly mimeType: string;
  readonly checksum: string;
}

export interface EncryptionMetadata {
  readonly algorithm: 'AES-256-GCM';
  readonly keyLength: number;
  readonly encrypted: boolean;
  readonly timestamp: string;
}

// =============================================================================
// AUDIO MESSAGE TYPES
// =============================================================================

export interface AudioMessage {
  readonly id: string;
  readonly sessionId: string;
  readonly userId: string;
  readonly speakerId: string;
  readonly speakerName: string;
  readonly messageType: 'user_voice' | 'agent_voice' | 'async_voice' | 'session_audio';
  readonly encryptedAudioData: EncryptedAudioData;
  readonly duration: number;
  readonly quality: 'low' | 'medium' | 'high';
  readonly transcription?: string;
  readonly confidence: number;
  readonly timestamp: string;
  readonly status: 'pending_sync' | 'synced' | 'failed';
  readonly size: number;
  readonly encrypted: boolean;
  readonly originalChecksum: string;
  readonly encryptedChecksum: string;
  readonly checksumAlgorithm: 'SHA-256';
  readonly metadata: AudioMessageMetadata;
}

export interface AudioMessageMetadata {
  readonly sampleRate: number;
  readonly bitRate: number;
  readonly format: string;
  readonly codec: string;
  readonly encryption: EncryptionMetadata;
  readonly integrity: IntegrityMetadata;
}

export interface StorageResult {
  readonly success: boolean;
  readonly messageId: string;
  readonly localStored: boolean;
  readonly cloudStored: boolean;
  readonly size: number;
  readonly encrypted: boolean;
}

// =============================================================================
// ERROR BOUNDARY TYPES
// =============================================================================

export interface AudioError extends Error {
  readonly code?: string;
  readonly constraint?: string;
  readonly audioContext?: AudioContextInfo;
  readonly severity?: 'low' | 'medium' | 'high' | 'critical';
  readonly recoverable?: boolean;
  readonly patientSafetyImpact?: boolean;
}

export interface AudioContextInfo {
  readonly deviceId?: string;
  readonly sampleRate?: number;
  readonly channelCount?: number;
  readonly state?: string;
}

export interface ErrorBoundaryState {
  readonly hasError: boolean;
  readonly error: AudioError | null;
  readonly errorInfo: React.ErrorInfo | null;
  readonly recoveryAttempts: number;
  readonly isRecovering: boolean;
  readonly fallbackMode: 'none' | 'text_only' | 'emergency_mode';
  readonly lastErrorTime: number;
  readonly sessionId?: string;
  readonly patientId?: string;
}

export interface RecoveryResult {
  readonly successful: boolean;
  readonly attemptNumber: number;
  readonly duration: number;
  readonly error?: string;
}

// =============================================================================
// SERVICE RESPONSE TYPES
// =============================================================================

export interface SpeechToTextResponse {
  readonly success: boolean;
  readonly data?: {
    readonly text: string;
    readonly confidence: number;
    readonly duration: number;
    readonly language: string;
    readonly processingTime: number;
  };
  readonly error?: string;
  readonly code?: string;
}

export interface TextToSpeechResponse {
  readonly success: boolean;
  readonly data?: {
    readonly audioData: string; // Base64 encoded
    readonly audioFormat: string;
    readonly duration: number;
    readonly voiceId: string;
    readonly processingTime: number;
  };
  readonly error?: string;
  readonly code?: string;
}

export interface AIResponse {
  readonly success: boolean;
  readonly data?: {
    readonly content: string;
    readonly agentType: string;
    readonly usage?: {
      readonly completion_tokens: number;
      readonly total_tokens: number;
    };
    readonly processingTime: number;
  };
  readonly error?: string;
  readonly code?: string;
}

// =============================================================================
// SERVICE OPTIONS TYPES
// =============================================================================

export interface SpeechToTextOptions {
  readonly sessionId: string;
  readonly language?: string;
  readonly temperature?: number;
  readonly emergencyOverride?: boolean;
  readonly emergencyToken?: string;
  readonly emergencyReason?: string;
  readonly emergencyAuthorizedBy?: string;
  readonly sessionToken: string;
}

export interface TextToSpeechOptions {
  readonly sessionId: string;
  readonly voiceId?: string;
  readonly agentType?: string;
  readonly stability?: number;
  readonly similarity_boost?: number;
  readonly emergencyOverride?: boolean;
  readonly emergencyToken?: string;
  readonly emergencyReason?: string;
  readonly emergencyAuthorizedBy?: string;
  readonly sessionToken: string;
}

export interface AIOrchestrationOptions {
  readonly sessionId: string;
  readonly messages: readonly ChatMessage[];
  readonly agentType?: string;
  readonly maxTokens?: number;
  readonly temperature?: number;
  readonly emergencyOverride?: boolean;
  readonly emergencyToken?: string;
  readonly emergencyReason?: string;
  readonly emergencyAuthorizedBy?: string;
  // Context enhancement fields
  readonly patientContext?: any;
  readonly assembledContext?: any;
  readonly regionalContext?: any;
  readonly medicalHistory?: any;
  readonly urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
}

export interface ChatMessage {
  readonly role: 'system' | 'user' | 'assistant';
  readonly content: string;
  readonly timestamp?: string;
}

// =============================================================================
// EMERGENCY PROTOCOL TYPES
// =============================================================================

export interface EmergencyStopEvent {
  readonly triggered: boolean;
  readonly reason: string;
  readonly timestamp: string;
  readonly responseTime: number; // Must be < 2000ms
  readonly userId: string;
  readonly sessionId: string;
}

export interface EmergencyProtocolResult {
  readonly success: boolean;
  readonly protocolsTriggered: readonly string[];
  readonly responseTime: number;
  readonly error?: string;
}

// =============================================================================
// BACKUP AND RECOVERY TYPES
// =============================================================================

export interface BackupResult {
  readonly success: boolean;
  readonly backupId: string;
  readonly location: 'local' | 'cloud' | 'both';
  readonly size: number;
  readonly checksum: string;
  readonly timestamp: string;
}

export interface RecoveryOptions {
  readonly source: 'local_backup' | 'cloud_backup' | 'auto';
  readonly verifyIntegrity: boolean;
  readonly emergencyAccess: boolean;
}

export interface DataRecoveryResult {
  readonly success: boolean;
  readonly source: 'local_backup' | 'cloud_backup';
  readonly data: AudioMessage;
  readonly integrityVerified: boolean;
  readonly recoveryTime: number;
}

// =============================================================================
// AUDIT AND LOGGING TYPES
// =============================================================================

export interface AudioAuditEvent {
  readonly eventType: string;
  readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly userId: string;
  readonly sessionId: string;
  readonly details: Record<string, unknown>;
  readonly timestamp: string;
}

export interface SecurityEvent extends AudioAuditEvent {
  readonly threatLevel: 'low' | 'medium' | 'high' | 'critical';
  readonly mitigationActions: readonly string[];
  readonly requiresInvestigation: boolean;
}

// =============================================================================
// GENERIC SERVICE TYPES
// =============================================================================

export interface ServiceResponse<T> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly code?: string;
  readonly timestamp: string;
}

export interface ServiceOptions {
  readonly timeout?: number;
  readonly retryAttempts?: number;
  readonly emergencyOverride?: boolean;
  readonly auditLogging?: boolean;
}

export interface HealthStatus {
  readonly healthy: boolean;
  readonly services: Record<string, boolean>;
  readonly lastCheck: string;
  readonly error?: string;
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export type AudioQuality = 'low' | 'medium' | 'high';
export type AudioFormat = 'webm' | 'wav' | 'mp3' | 'm4a' | 'ogg';
export type EncryptionAlgorithm = 'AES-256-GCM';
export type ChecksumAlgorithm = 'SHA-256';
export type ValidationSeverity = 'low' | 'medium' | 'high' | 'critical';

// Type guards for runtime type checking
export const isAudioError = (error: Error): error is AudioError => {
  return 'severity' in error || 'recoverable' in error || 'audioContext' in error;
};

export const isValidationResult = (obj: unknown): obj is ValidationResult => {
  return typeof obj === 'object' && obj !== null && 'valid' in obj && 'errors' in obj;
};

export const isServiceResponse = <T>(obj: unknown): obj is ServiceResponse<T> => {
  return typeof obj === 'object' && obj !== null && 'success' in obj;
};
