#!/usr/bin/env tsx

/**
 * POST-IMPLEMENTATION VALIDATION SCRIPT
 * 
 * Validates the findings from the comprehensive post-implementation review.
 * Checks for missing service methods, integration gaps, and implementation issues.
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

interface ValidationIssue {
  category: string;
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  issue: string;
  impact: string;
  recommendation: string;
}

class PostImplementationValidator {
  private issues: ValidationIssue[] = [];

  constructor() {
    console.log(`${colors.cyan}🔍 VoiceHealth AI - Post-Implementation Validation${colors.reset}`);
    console.log(`${colors.cyan}===================================================${colors.reset}\n`);
  }

  async runValidation(): Promise<void> {
    try {
      await this.validateServiceMethods();
      await this.validatePerformanceMonitoring();
      await this.validateErrorHandling();
      await this.validateServiceIntegration();
      await this.validateDatabaseIntegrity();
      await this.validateRegionalConfigurations();
      await this.validateTestCoverage();
      
      this.printResults();
      
      const criticalIssues = this.issues.filter(i => i.severity === 'CRITICAL');
      if (criticalIssues.length > 0) {
        console.log(`\n${colors.red}🚨 ${criticalIssues.length} CRITICAL issues found - Production deployment blocked${colors.reset}`);
        process.exit(1);
      } else {
        console.log(`\n${colors.green}✅ No critical issues found${colors.reset}`);
        process.exit(0);
      }
    } catch (error) {
      console.error(`${colors.red}❌ Validation failed:${colors.reset}`, error);
      process.exit(1);
    }
  }

  private async validateServiceMethods(): Promise<void> {
    console.log(`${colors.blue}🔧 Validating Service Method Implementations...${colors.reset}`);
    
    const expectedMethods = {
      'ClinicalDocumentationService': [
        'structureNoteFromEntities',
        'applyCulturalAdaptations', 
        'generateDataHash',
        'generateICD10Suggestions',
        'generateCPTSuggestions',
        'assessCompleteness',
        'assessAccuracy',
        'assessClarity',
        'assessCulturalSensitivity',
        'assessCompliance',
        'generateImprovementSuggestions'
      ],
      'AdvancedRiskStratificationService': [
        'predictDiseaseProgression',
        'predictHospitalizationRisk',
        'predictMortalityRisk',
        'predictComplicationRisk',
        'predictTreatmentResponse',
        'calculateRegionalRiskScore',
        'calculateModifiableRiskScore',
        'calculateNonModifiableRiskScore'
      ],
      'CulturalValidationService': [
        'getCulturallySensitiveTerms',
        'assessCulturalAppropriateness',
        'assessReadingLevel',
        'getAppropriateReadingLevel',
        'assessCulturalLanguagePatterns',
        'extractCulturalReferences',
        'checkCulturalReferenceAccuracy',
        'detectGenderBias',
        'detectAgeBias',
        'detectEthnicBias',
        'generateBiasMitigationStrategies'
      ]
    };

    let totalMissingMethods = 0;

    for (const [serviceName, methods] of Object.entries(expectedMethods)) {
      const servicePath = path.join(process.cwd(), `src/services/${serviceName}.ts`);
      
      if (!existsSync(servicePath)) {
        this.addIssue('Service Methods', 'CRITICAL', 
          `${serviceName} file not found`,
          'Service will not be available',
          `Create ${serviceName}.ts file`);
        continue;
      }

      const serviceContent = readFileSync(servicePath, 'utf8');
      const missingMethods = methods.filter(method => 
        !serviceContent.includes(`async ${method}(`) && 
        !serviceContent.includes(`${method}(`) &&
        !serviceContent.includes(`${method}:`)
      );

      if (missingMethods.length > 0) {
        totalMissingMethods += missingMethods.length;
        this.addIssue('Service Methods', 'CRITICAL',
          `${serviceName}: ${missingMethods.length} missing methods - ${missingMethods.join(', ')}`,
          'Runtime errors when methods are called',
          `Implement missing methods in ${serviceName}`);
      }
    }

    if (totalMissingMethods === 0) {
      console.log(`  ${colors.green}✅ All expected service methods found${colors.reset}`);
    } else {
      console.log(`  ${colors.red}❌ ${totalMissingMethods} missing service methods${colors.reset}`);
    }
  }

  private async validatePerformanceMonitoring(): Promise<void> {
    console.log(`${colors.blue}📊 Validating Performance Monitoring Integration...${colors.reset}`);
    
    const servicesToCheck = [
      'ClinicalDocumentationService',
      'AdvancedRiskStratificationService', 
      'CulturalValidationService',
      'AuthenticationService',
      'EncryptionService'
    ];

    let servicesWithMonitoring = 0;

    for (const serviceName of servicesToCheck) {
      const servicePath = path.join(process.cwd(), `src/services/${serviceName}.ts`);
      
      if (existsSync(servicePath)) {
        const serviceContent = readFileSync(servicePath, 'utf8');
        
        if (serviceContent.includes('@MonitorPerformance') || 
            serviceContent.includes('MonitorPerformance')) {
          servicesWithMonitoring++;
        } else {
          this.addIssue('Performance Monitoring', 'CRITICAL',
            `${serviceName}: No performance monitoring applied`,
            'No performance visibility for core operations',
            `Add @MonitorPerformance decorators to ${serviceName} methods`);
        }
      }
    }

    if (servicesWithMonitoring === servicesToCheck.length) {
      console.log(`  ${colors.green}✅ Performance monitoring applied to all services${colors.reset}`);
    } else {
      console.log(`  ${colors.red}❌ Performance monitoring missing from ${servicesToCheck.length - servicesWithMonitoring} services${colors.reset}`);
    }
  }

  private async validateErrorHandling(): Promise<void> {
    console.log(`${colors.blue}⚠️  Validating Standardized Error Handling...${colors.reset}`);
    
    const servicesToCheck = [
      'ClinicalDocumentationService',
      'AdvancedRiskStratificationService',
      'CulturalValidationService',
      'AuthenticationService',
      'EncryptionService'
    ];

    let servicesWithStandardErrorHandling = 0;

    for (const serviceName of servicesToCheck) {
      const servicePath = path.join(process.cwd(), `src/services/${serviceName}.ts`);
      
      if (existsSync(servicePath)) {
        const serviceContent = readFileSync(servicePath, 'utf8');
        
        if (serviceContent.includes('standardErrorHandler') || 
            serviceContent.includes('handleServiceError')) {
          servicesWithStandardErrorHandling++;
        } else {
          this.addIssue('Error Handling', 'CRITICAL',
            `${serviceName}: Using old error handling patterns`,
            'Inconsistent error responses, potential PHI exposure',
            `Apply standardErrorHandler to ${serviceName}`);
        }
      }
    }

    if (servicesWithStandardErrorHandling === servicesToCheck.length) {
      console.log(`  ${colors.green}✅ Standardized error handling applied to all services${colors.reset}`);
    } else {
      console.log(`  ${colors.red}❌ Standardized error handling missing from ${servicesToCheck.length - servicesWithStandardErrorHandling} services${colors.reset}`);
    }
  }

  private async validateServiceIntegration(): Promise<void> {
    console.log(`${colors.blue}🔗 Validating Service Integration...${colors.reset}`);
    
    // Check AI Orchestrator integration
    const orchestratorPath = path.join(process.cwd(), 'src/services/aiOrchestrator.ts');
    if (existsSync(orchestratorPath)) {
      const orchestratorContent = readFileSync(orchestratorPath, 'utf8');
      
      // Check for method calls that might not exist
      const methodCalls = [
        'performRiskAssessment(',
        'validateCulturalContent(',
        'generateVoiceToNote('
      ];

      const missingCalls = methodCalls.filter(call => !orchestratorContent.includes(call));
      
      if (missingCalls.length > 0) {
        this.addIssue('Service Integration', 'CRITICAL',
          `AI Orchestrator: Calls to non-existent methods - ${missingCalls.join(', ')}`,
          'Runtime errors when orchestrator calls services',
          'Fix method calls in AI Orchestrator to match actual service methods');
      }
    }

    // Check for authentication integration in services
    const servicesWithAuth = ['ClinicalDocumentationService', 'AdvancedRiskStratificationService'];
    let authIntegrationCount = 0;

    for (const serviceName of servicesWithAuth) {
      const servicePath = path.join(process.cwd(), `src/services/${serviceName}.ts`);
      if (existsSync(servicePath)) {
        const serviceContent = readFileSync(servicePath, 'utf8');
        
        if (serviceContent.includes('authenticationService') || 
            serviceContent.includes('getCurrentUser')) {
          authIntegrationCount++;
        } else {
          this.addIssue('Service Integration', 'HIGH',
            `${serviceName}: No authentication integration`,
            'Services process requests without authentication validation',
            `Add authentication validation to ${serviceName} methods`);
        }
      }
    }

    console.log(`  ${colors.yellow}⚠️  Authentication integration: ${authIntegrationCount}/${servicesWithAuth.length} services${colors.reset}`);
  }

  private async validateDatabaseIntegrity(): Promise<void> {
    console.log(`${colors.blue}🗄️  Validating Database Integrity...${colors.reset}`);
    
    const migrationPath = path.join(process.cwd(), 'supabase/migrations/20250106000001_fix_foreign_keys.sql');
    
    if (!existsSync(migrationPath)) {
      this.addIssue('Database Integrity', 'HIGH',
        'Foreign key migration file not found',
        'Database relationships not enforced',
        'Create and apply foreign key migration');
      return;
    }

    const migrationContent = readFileSync(migrationPath, 'utf8');
    const requiredConstraints = [
      'fk_medical_translations_verified_by',
      'fk_focus_groups_facilitator',
      'fk_performance_metrics_component'
    ];

    const missingConstraints = requiredConstraints.filter(constraint => 
      !migrationContent.includes(constraint)
    );

    if (missingConstraints.length > 0) {
      this.addIssue('Database Integrity', 'HIGH',
        `Missing foreign key constraints: ${missingConstraints.join(', ')}`,
        'Data integrity not enforced',
        'Add missing foreign key constraints to migration');
    } else {
      console.log(`  ${colors.green}✅ Foreign key migration contains required constraints${colors.reset}`);
    }
  }

  private async validateRegionalConfigurations(): Promise<void> {
    console.log(`${colors.blue}🌍 Validating Regional Configurations...${colors.reset}`);
    
    const requiredRegions = ['ghana', 'kenya', 'nigeria', 'south-africa', 'ethiopia'];
    const configPath = path.join(process.cwd(), 'config/regions');
    
    if (!existsSync(configPath)) {
      this.addIssue('Regional Configurations', 'HIGH',
        'Regional configuration directory not found',
        'Regional deployments will fail',
        'Create config/regions directory with configuration files');
      return;
    }

    const missingConfigs = requiredRegions.filter(region => 
      !existsSync(path.join(configPath, `${region}.json`))
    );

    if (missingConfigs.length > 0) {
      this.addIssue('Regional Configurations', 'HIGH',
        `Missing configuration files: ${missingConfigs.join(', ')}`,
        'Regional deployments will fail for missing countries',
        'Create missing regional configuration files');
    } else {
      console.log(`  ${colors.green}✅ All regional configuration files present${colors.reset}`);
    }
  }

  private async validateTestCoverage(): Promise<void> {
    console.log(`${colors.blue}🧪 Validating Test Coverage Quality...${colors.reset}`);
    
    const testFiles = [
      'src/tests/integration/cross-phase-integration.test.ts',
      'src/tests/integration/end-to-end-workflows.test.ts'
    ];

    for (const testFile of testFiles) {
      if (existsSync(path.join(process.cwd(), testFile))) {
        const testContent = readFileSync(path.join(process.cwd(), testFile), 'utf8');
        
        // Check for excessive mocking
        const mockCount = (testContent.match(/\.mockResolvedValue|\.mockImplementation/g) || []).length;
        const testCount = (testContent.match(/it\(/g) || []).length;
        
        if (mockCount > testCount * 2) {
          this.addIssue('Test Coverage', 'MEDIUM',
            `${testFile}: Excessive mocking (${mockCount} mocks for ${testCount} tests)`,
            'Tests provide false confidence due to mocking',
            'Rewrite tests to use real service implementations');
        }
      } else {
        this.addIssue('Test Coverage', 'MEDIUM',
          `Missing test file: ${testFile}`,
          'Integration scenarios not tested',
          'Create missing test files');
      }
    }
  }

  private addIssue(category: string, severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW', 
                   issue: string, impact: string, recommendation: string): void {
    this.issues.push({ category, severity, issue, impact, recommendation });
    
    const severityColor = {
      'CRITICAL': colors.red,
      'HIGH': colors.yellow,
      'MEDIUM': colors.blue,
      'LOW': colors.cyan
    }[severity];
    
    console.log(`  ${severityColor}${severity}${colors.reset}: ${issue}`);
  }

  private printResults(): void {
    console.log(`\n${colors.cyan}📊 Post-Implementation Review Summary${colors.reset}`);
    console.log(`${colors.cyan}=====================================${colors.reset}`);
    
    const severityCounts = {
      'CRITICAL': this.issues.filter(i => i.severity === 'CRITICAL').length,
      'HIGH': this.issues.filter(i => i.severity === 'HIGH').length,
      'MEDIUM': this.issues.filter(i => i.severity === 'MEDIUM').length,
      'LOW': this.issues.filter(i => i.severity === 'LOW').length
    };
    
    console.log(`\n${colors.red}🔴 Critical Issues: ${severityCounts.CRITICAL}${colors.reset}`);
    console.log(`${colors.yellow}🟠 High Priority Issues: ${severityCounts.HIGH}${colors.reset}`);
    console.log(`${colors.blue}🟡 Medium Priority Issues: ${severityCounts.MEDIUM}${colors.reset}`);
    console.log(`${colors.cyan}🔵 Low Priority Issues: ${severityCounts.LOW}${colors.reset}`);
    
    const totalIssues = Object.values(severityCounts).reduce((sum, count) => sum + count, 0);
    console.log(`\n${colors.white}Total Issues Found: ${totalIssues}${colors.reset}`);
    
    if (severityCounts.CRITICAL > 0) {
      console.log(`\n${colors.red}🚨 PRODUCTION DEPLOYMENT BLOCKED${colors.reset}`);
      console.log(`${colors.red}Critical issues must be resolved before deployment${colors.reset}`);
    } else if (severityCounts.HIGH > 0) {
      console.log(`\n${colors.yellow}⚠️  HIGH PRIORITY ISSUES FOUND${colors.reset}`);
      console.log(`${colors.yellow}Recommend resolving before production deployment${colors.reset}`);
    } else {
      console.log(`\n${colors.green}✅ NO CRITICAL ISSUES FOUND${colors.reset}`);
    }
  }
}

// Run validation if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new PostImplementationValidator();
  validator.runValidation().catch(error => {
    console.error(`${colors.red}Post-implementation validation failed:${colors.reset}`, error);
    process.exit(1);
  });
}

export { PostImplementationValidator };
