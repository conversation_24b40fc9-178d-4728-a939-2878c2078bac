import React from "react";
import Navbar from "components/Navbar";
import Footer from "components/Footer";
import { Link } from "react-router-dom";

const plans = [
  {
    name: "Starter",
    price: "$0",
    desc: "Free forever for personal use",
    features: ["1 consultation / month", "Community support"]
  },
  {
    name: "Pro",
    price: "$29",
    desc: "For power users and small teams",
    features: ["Unlimited consultations", "Priority AI models", "Email support"]
  },
  {
    name: "Enterprise",
    price: "Contact us",
    desc: "Custom deployments & SLAs",
    features: ["Dedicated cluster", "Custom SLA", "White-glove onboarding"]
  }
];

const PricingPage = () => (
  <>
    <Navbar />
    <main className="max-w-7xl mx-auto py-24 px-4 sm:px-6 lg:px-8 text-center">
      <h1 className="text-4xl font-extrabold text-text-primary mb-6">Simple, transparent pricing</h1>
      <p className="text-lg text-text-secondary mb-12">Choose a plan that fits your needs.</p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {plans.map((p) => (
          <div key={p.name} className="border rounded-lg shadow-sm p-6 flex flex-col">
            <h3 className="text-2xl font-semibold mb-2 text-text-primary">{p.name}</h3>
            <p className="text-3xl font-bold text-primary-600 mb-4">{p.price}</p>
            <p className="text-text-secondary mb-6 flex-1">{p.desc}</p>
            <ul className="text-left space-y-2 mb-6">
              {p.features.map((f) => (
                <li key={f} className="text-text-secondary flex items-start">
                  <svg className="h-5 w-5 text-primary-600 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.707a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  {f}
                </li>
              ))}
            </ul>
            {p.name !== "Enterprise" ? (
              <Link
                to="/payment-plans"
                className="inline-flex justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                Get Started
              </Link>
            ) : (
              <a
                href="mailto:<EMAIL>"
                className="inline-flex justify-center px-4 py-2 border border-primary-600 text-sm font-medium rounded-md text-primary-600 hover:bg-primary-50"
              >
                Contact Sales
              </a>
            )}
          </div>
        ))}
      </div>
    </main>
    <Footer />
  </>
);

export default PricingPage;
