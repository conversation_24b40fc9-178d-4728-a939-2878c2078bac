import React, { useState } from "react";
import Navbar from "components/Navbar";
import Footer from "components/Footer";

const ContactPage = () => {
  const [state, setState] = useState({ name: "", email: "", message: "" });
  const [submitted, setSubmitted] = useState(false);

  const handleChange = (e) => {
    setState({ ...state, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // TODO: integrate email service or backend endpoint
    setSubmitted(true);
  };

  return (
    <>
      <Navbar />
      <main className="max-w-xl mx-auto py-24 px-4 sm:px-6 lg:px-8">
        <h1 className="text-3xl font-extrabold text-text-primary mb-6 text-center">Contact Us</h1>
        {submitted ? (
          <p className="text-center text-primary-600">Thank you! We'll get back to you shortly.</p>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-text-primary">
                Name
              </label>
              <input
                type="text"
                name="name"
                id="name"
                required
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-600 focus:border-primary-600"
                value={state.name}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-text-primary">
                Email
              </label>
              <input
                type="email"
                name="email"
                id="email"
                required
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-600 focus:border-primary-600"
                value={state.email}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-text-primary">
                Message
              </label>
              <textarea
                name="message"
                id="message"
                rows="4"
                required
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-600 focus:border-primary-600"
                value={state.message}
                onChange={handleChange}
              />
            </div>
            <button
              type="submit"
              className="inline-flex justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              Send Message
            </button>
          </form>
        )}
      </main>
      <Footer />
    </>
  );
};

export default ContactPage;
