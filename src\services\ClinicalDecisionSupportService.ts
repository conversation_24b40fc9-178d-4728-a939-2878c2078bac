/**
 * Clinical Decision Support Service
 * 
 * Provides intelligent clinical decision support including differential diagnosis,
 * red flag detection, and specialist referral recommendations.
 */

import { SOAPAssessment } from './DiagnosticFrameworkService';

export interface ClinicalDecision {
  differentialDiagnoses: DifferentialDiagnosis[];
  redFlags: RedFlag[];
  specialistReferrals: SpecialistReferral[];
  urgencyLevel: 'routine' | 'urgent' | 'emergent' | 'immediate';
  recommendedActions: RecommendedAction[];
  clinicalReasoning: string;
  confidenceLevel: number; // 0-100
}

export interface DifferentialDiagnosis {
  condition: string;
  probability: number; // 0-100
  supportingEvidence: string[];
  contradictingEvidence: string[];
  keyQuestions: string[];
  diagnosticTests: string[];
  urgencyLevel: 'routine' | 'urgent' | 'emergent';
}

export interface RedFlag {
  flag: string;
  severity: 'yellow' | 'orange' | 'red';
  description: string;
  immediateAction: string;
  timeframe: string; // e.g., "within 1 hour", "immediately"
  specialtyConsult?: string;
  evidenceBased: boolean;
}

export interface SpecialistReferral {
  specialty: string;
  urgency: 'routine' | 'urgent' | 'emergent' | 'immediate';
  reason: string;
  expectedTimeframe: string;
  preparationNeeded: string[];
  keyInformation: string[];
}

export interface RecommendedAction {
  action: string;
  priority: 'immediate' | 'high' | 'medium' | 'low';
  timeframe: string;
  rationale: string;
  contraindications?: string[];
}

export interface ClinicalContext {
  chiefComplaint: string;
  symptoms: string[];
  patientAge: number;
  patientGender: 'male' | 'female' | 'other';
  vitalSigns?: VitalSigns;
  medicalHistory: string[];
  medications: string[];
  allergies: string[];
  socialHistory?: SocialHistory;
}

export interface VitalSigns {
  temperature?: number;
  bloodPressure?: { systolic: number; diastolic: number };
  heartRate?: number;
  respiratoryRate?: number;
  oxygenSaturation?: number;
  painScore?: number;
}

export interface SocialHistory {
  smoking: boolean;
  alcohol: boolean;
  drugs: boolean;
  occupation?: string;
}

class ClinicalDecisionSupportService {
  
  // Red flag criteria by symptom category
  private readonly RED_FLAGS = {
    chest_pain: [
      {
        flag: 'Acute MI symptoms',
        criteria: ['crushing chest pain', 'radiation to arm/jaw', 'diaphoresis', 'nausea'],
        severity: 'red' as const,
        action: 'Call 911 immediately',
        timeframe: 'immediately'
      },
      {
        flag: 'Aortic dissection',
        criteria: ['tearing chest pain', 'back pain', 'blood pressure difference'],
        severity: 'red' as const,
        action: 'Emergency department immediately',
        timeframe: 'immediately'
      }
    ],
    shortness_of_breath: [
      {
        flag: 'Pulmonary embolism',
        criteria: ['sudden onset', 'chest pain', 'recent surgery/travel'],
        severity: 'red' as const,
        action: 'Emergency evaluation',
        timeframe: 'within 1 hour'
      },
      {
        flag: 'Severe asthma exacerbation',
        criteria: ['unable to speak sentences', 'accessory muscle use'],
        severity: 'red' as const,
        action: 'Emergency treatment',
        timeframe: 'immediately'
      }
    ],
    headache: [
      {
        flag: 'Subarachnoid hemorrhage',
        criteria: ['worst headache of life', 'sudden onset', 'neck stiffness'],
        severity: 'red' as const,
        action: 'Emergency department immediately',
        timeframe: 'immediately'
      },
      {
        flag: 'Meningitis',
        criteria: ['fever', 'neck stiffness', 'photophobia', 'altered mental status'],
        severity: 'red' as const,
        action: 'Emergency evaluation',
        timeframe: 'within 1 hour'
      }
    ],
    abdominal_pain: [
      {
        flag: 'Appendicitis',
        criteria: ['right lower quadrant pain', 'fever', 'nausea', 'rebound tenderness'],
        severity: 'orange' as const,
        action: 'Urgent surgical evaluation',
        timeframe: 'within 4 hours'
      },
      {
        flag: 'Bowel obstruction',
        criteria: ['severe cramping', 'vomiting', 'no bowel movements', 'distension'],
        severity: 'red' as const,
        action: 'Emergency evaluation',
        timeframe: 'within 2 hours'
      }
    ]
  };

  // Differential diagnosis patterns
  private readonly DIFFERENTIAL_PATTERNS = {
    chest_pain: [
      {
        condition: 'Myocardial Infarction',
        keyFeatures: ['crushing pain', 'radiation', 'diaphoresis', 'nausea'],
        riskFactors: ['age > 45', 'diabetes', 'hypertension', 'smoking'],
        urgency: 'emergent' as const
      },
      {
        condition: 'Gastroesophageal Reflux',
        keyFeatures: ['burning pain', 'worse after eating', 'relieved by antacids'],
        riskFactors: ['obesity', 'hiatal hernia'],
        urgency: 'routine' as const
      },
      {
        condition: 'Musculoskeletal Pain',
        keyFeatures: ['sharp pain', 'worse with movement', 'tender to touch'],
        riskFactors: ['recent activity', 'trauma'],
        urgency: 'routine' as const
      }
    ],
    shortness_of_breath: [
      {
        condition: 'Asthma Exacerbation',
        keyFeatures: ['wheezing', 'cough', 'chest tightness'],
        riskFactors: ['history of asthma', 'allergies', 'recent URI'],
        urgency: 'urgent' as const
      },
      {
        condition: 'Pneumonia',
        keyFeatures: ['fever', 'productive cough', 'chest pain'],
        riskFactors: ['age > 65', 'immunocompromised', 'chronic illness'],
        urgency: 'urgent' as const
      }
    ]
  };

  // Specialist referral criteria
  private readonly REFERRAL_CRITERIA = {
    cardiology: {
      urgent: ['chest pain with ECG changes', 'heart failure exacerbation'],
      routine: ['hypertension management', 'lipid disorders']
    },
    pulmonology: {
      urgent: ['severe asthma', 'suspected pulmonary embolism'],
      routine: ['chronic cough', 'sleep apnea evaluation']
    },
    gastroenterology: {
      urgent: ['GI bleeding', 'severe abdominal pain'],
      routine: ['GERD management', 'screening colonoscopy']
    },
    neurology: {
      urgent: ['severe headache', 'neurological deficits'],
      routine: ['chronic headaches', 'memory concerns']
    }
  };

  /**
   * Generate clinical decision support
   */
  generateClinicalDecision(context: ClinicalContext, soapAssessment: SOAPAssessment): ClinicalDecision {
    // Detect red flags
    const redFlags = this.detectRedFlags(context);
    
    // Generate differential diagnoses
    const differentialDiagnoses = this.generateDifferentialDiagnoses(context);
    
    // Determine specialist referrals
    const specialistReferrals = this.determineSpecialistReferrals(context, redFlags);
    
    // Assess overall urgency
    const urgencyLevel = this.assessUrgencyLevel(redFlags, differentialDiagnoses);
    
    // Generate recommended actions
    const recommendedActions = this.generateRecommendedActions(context, redFlags, urgencyLevel);
    
    // Calculate confidence level
    const confidenceLevel = this.calculateConfidenceLevel(context, soapAssessment);
    
    return {
      differentialDiagnoses,
      redFlags,
      specialistReferrals,
      urgencyLevel,
      recommendedActions,
      clinicalReasoning: this.generateClinicalReasoning(context, differentialDiagnoses, redFlags),
      confidenceLevel
    };
  }

  /**
   * Detect red flags based on symptoms and context
   */
  private detectRedFlags(context: ClinicalContext): RedFlag[] {
    const redFlags: RedFlag[] = [];
    const complaint = context.chiefComplaint.toLowerCase();
    
    // Determine symptom category
    let category: string | null = null;
    if (complaint.includes('chest pain')) category = 'chest_pain';
    else if (complaint.includes('shortness of breath')) category = 'shortness_of_breath';
    else if (complaint.includes('headache')) category = 'headache';
    else if (complaint.includes('abdominal pain')) category = 'abdominal_pain';
    
    if (!category || !this.RED_FLAGS[category]) return redFlags;
    
    // Check each red flag for this category
    this.RED_FLAGS[category].forEach(redFlagDef => {
      const matchingCriteria = redFlagDef.criteria.filter(criterion => 
        context.symptoms.some(symptom => 
          symptom.toLowerCase().includes(criterion.toLowerCase())
        ) || context.chiefComplaint.toLowerCase().includes(criterion.toLowerCase())
      );
      
      if (matchingCriteria.length >= 2) { // Require at least 2 criteria
        redFlags.push({
          flag: redFlagDef.flag,
          severity: redFlagDef.severity,
          description: `Patient presents with ${matchingCriteria.join(', ')}`,
          immediateAction: redFlagDef.action,
          timeframe: redFlagDef.timeframe,
          evidenceBased: true
        });
      }
    });
    
    // Check vital signs for red flags
    if (context.vitalSigns) {
      redFlags.push(...this.checkVitalSignRedFlags(context.vitalSigns));
    }
    
    return redFlags;
  }

  /**
   * Generate differential diagnoses
   */
  private generateDifferentialDiagnoses(context: ClinicalContext): DifferentialDiagnosis[] {
    const differentials: DifferentialDiagnosis[] = [];
    const complaint = context.chiefComplaint.toLowerCase();
    
    // Determine symptom category
    let category: string | null = null;
    if (complaint.includes('chest pain')) category = 'chest_pain';
    else if (complaint.includes('shortness of breath')) category = 'shortness_of_breath';
    
    if (!category || !this.DIFFERENTIAL_PATTERNS[category]) return differentials;
    
    // Generate differentials for this category
    this.DIFFERENTIAL_PATTERNS[category].forEach(pattern => {
      const supportingEvidence = this.findSupportingEvidence(pattern, context);
      const probability = this.calculateProbability(pattern, context, supportingEvidence);
      
      if (probability > 10) { // Only include if probability > 10%
        differentials.push({
          condition: pattern.condition,
          probability,
          supportingEvidence,
          contradictingEvidence: [],
          keyQuestions: this.generateKeyQuestions(pattern),
          diagnosticTests: this.suggestDiagnosticTests(pattern),
          urgencyLevel: pattern.urgency
        });
      }
    });
    
    // Sort by probability
    return differentials.sort((a, b) => b.probability - a.probability);
  }

  /**
   * Determine specialist referrals needed
   */
  private determineSpecialistReferrals(context: ClinicalContext, redFlags: RedFlag[]): SpecialistReferral[] {
    const referrals: SpecialistReferral[] = [];
    
    // Check for urgent referrals based on red flags
    redFlags.forEach(flag => {
      if (flag.severity === 'red') {
        if (flag.flag.includes('MI') || flag.flag.includes('cardiac')) {
          referrals.push({
            specialty: 'Cardiology',
            urgency: 'immediate',
            reason: flag.description,
            expectedTimeframe: 'within 1 hour',
            preparationNeeded: ['ECG', 'cardiac enzymes'],
            keyInformation: [context.chiefComplaint, 'vital signs', 'cardiac risk factors']
          });
        }
      }
    });
    
    // Check for routine referrals based on symptoms
    const complaint = context.chiefComplaint.toLowerCase();
    if (complaint.includes('chest pain') && redFlags.length === 0) {
      referrals.push({
        specialty: 'Cardiology',
        urgency: 'routine',
        reason: 'Chest pain evaluation',
        expectedTimeframe: 'within 2 weeks',
        preparationNeeded: ['ECG', 'basic metabolic panel'],
        keyInformation: ['symptom characteristics', 'cardiac risk factors']
      });
    }
    
    return referrals;
  }

  /**
   * Assess overall urgency level
   */
  private assessUrgencyLevel(redFlags: RedFlag[], differentials: DifferentialDiagnosis[]): 'routine' | 'urgent' | 'emergent' | 'immediate' {
    // Check for immediate/red flags
    if (redFlags.some(flag => flag.severity === 'red')) {
      return 'immediate';
    }
    
    // Check for emergent conditions
    if (differentials.some(diff => diff.urgencyLevel === 'emergent')) {
      return 'emergent';
    }
    
    // Check for urgent conditions
    if (redFlags.some(flag => flag.severity === 'orange') || 
        differentials.some(diff => diff.urgencyLevel === 'urgent')) {
      return 'urgent';
    }
    
    return 'routine';
  }

  /**
   * Generate recommended actions
   */
  private generateRecommendedActions(
    context: ClinicalContext, 
    redFlags: RedFlag[], 
    urgencyLevel: string
  ): RecommendedAction[] {
    const actions: RecommendedAction[] = [];
    
    // Add actions based on urgency
    if (urgencyLevel === 'immediate') {
      actions.push({
        action: 'Call 911 or go to emergency department immediately',
        priority: 'immediate',
        timeframe: 'now',
        rationale: 'Life-threatening condition suspected'
      });
    } else if (urgencyLevel === 'emergent') {
      actions.push({
        action: 'Seek emergency medical care within 1 hour',
        priority: 'high',
        timeframe: 'within 1 hour',
        rationale: 'Serious condition requiring prompt evaluation'
      });
    }
    
    // Add symptom-specific actions
    actions.push(...this.generateSymptomSpecificActions(context));
    
    return actions;
  }

  // Helper methods
  private checkVitalSignRedFlags(vitals: VitalSigns): RedFlag[] {
    const flags: RedFlag[] = [];
    
    if (vitals.temperature && vitals.temperature > 103) {
      flags.push({
        flag: 'High fever',
        severity: 'orange',
        description: `Temperature ${vitals.temperature}°F`,
        immediateAction: 'Urgent medical evaluation',
        timeframe: 'within 2 hours',
        evidenceBased: true
      });
    }
    
    if (vitals.oxygenSaturation && vitals.oxygenSaturation < 90) {
      flags.push({
        flag: 'Severe hypoxemia',
        severity: 'red',
        description: `Oxygen saturation ${vitals.oxygenSaturation}%`,
        immediateAction: 'Emergency oxygen therapy',
        timeframe: 'immediately',
        evidenceBased: true
      });
    }
    
    return flags;
  }

  private findSupportingEvidence(pattern: any, context: ClinicalContext): string[] {
    const evidence: string[] = [];
    
    pattern.keyFeatures.forEach((feature: string) => {
      if (context.symptoms.some(symptom => symptom.toLowerCase().includes(feature.toLowerCase()))) {
        evidence.push(feature);
      }
    });
    
    return evidence;
  }

  private calculateProbability(pattern: any, context: ClinicalContext, evidence: string[]): number {
    let probability = 20; // Base probability
    
    // Increase probability for each supporting feature
    probability += evidence.length * 15;
    
    // Adjust for risk factors
    pattern.riskFactors.forEach((factor: string) => {
      if (this.hasRiskFactor(factor, context)) {
        probability += 10;
      }
    });
    
    return Math.min(probability, 95); // Cap at 95%
  }

  private hasRiskFactor(factor: string, context: ClinicalContext): boolean {
    if (factor.includes('age') && factor.includes('>')) {
      const ageThreshold = parseInt(factor.match(/\d+/)?.[0] || '0');
      return context.patientAge > ageThreshold;
    }
    
    return context.medicalHistory.some(condition => 
      condition.toLowerCase().includes(factor.toLowerCase())
    );
  }

  private generateKeyQuestions(pattern: any): string[] {
    // Generate condition-specific questions
    return [
      `Have you experienced ${pattern.keyFeatures[0]} before?`,
      'What makes this symptom better or worse?',
      'Any family history of similar conditions?'
    ];
  }

  private suggestDiagnosticTests(pattern: any): string[] {
    // Suggest appropriate tests based on condition
    if (pattern.condition.includes('Myocardial')) {
      return ['ECG', 'Cardiac enzymes', 'Chest X-ray'];
    }
    if (pattern.condition.includes('Pneumonia')) {
      return ['Chest X-ray', 'CBC', 'Blood cultures'];
    }
    return ['Basic metabolic panel', 'CBC'];
  }

  private generateSymptomSpecificActions(context: ClinicalContext): RecommendedAction[] {
    const actions: RecommendedAction[] = [];
    
    if (context.chiefComplaint.toLowerCase().includes('pain')) {
      actions.push({
        action: 'Consider appropriate pain management',
        priority: 'medium',
        timeframe: 'as needed',
        rationale: 'Patient comfort and symptom relief'
      });
    }
    
    return actions;
  }

  private calculateConfidenceLevel(context: ClinicalContext, soapAssessment: SOAPAssessment): number {
    let confidence = 50; // Base confidence
    
    // Increase confidence with more complete information
    if (context.vitalSigns) confidence += 15;
    if (context.medicalHistory.length > 0) confidence += 10;
    if (context.symptoms.length > 2) confidence += 10;
    if (soapAssessment.completionPercentage > 75) confidence += 15;
    
    return Math.min(confidence, 95);
  }

  private generateClinicalReasoning(
    context: ClinicalContext, 
    differentials: DifferentialDiagnosis[], 
    redFlags: RedFlag[]
  ): string {
    let reasoning = `Based on the chief complaint of "${context.chiefComplaint}" `;
    
    if (redFlags.length > 0) {
      reasoning += `and presence of red flags (${redFlags.map(f => f.flag).join(', ')}), `;
    }
    
    if (differentials.length > 0) {
      reasoning += `the most likely diagnosis is ${differentials[0].condition} (${differentials[0].probability}% probability). `;
    }
    
    reasoning += 'Recommend systematic evaluation and appropriate follow-up based on urgency level.';
    
    return reasoning;
  }
}

export const clinicalDecisionSupportService = new ClinicalDecisionSupportService();
export default clinicalDecisionSupportService;
